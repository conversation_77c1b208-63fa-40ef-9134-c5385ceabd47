import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/room_messages_list.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_input_field.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_message_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 音频房间聊天区域组件
/// 整合了传统房间消息和新的文字聊天功能
class AudioRoomChatSection extends ConsumerStatefulWidget {
  const AudioRoomChatSection({super.key});

  @override
  ConsumerState<AudioRoomChatSection> createState() =>
      _AudioRoomChatSectionState();
}

class _AudioRoomChatSectionState extends ConsumerState<AudioRoomChatSection> {
  bool _showChatInput = false;
  bool _isChatInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeChat();
  }

  /// 初始化聊天功能
  Future<void> _initializeChat() async {
    final result =
        await ref.read(audioRoomProvider.notifier).initializeChatService();
    if (result.isRight()) {
      setState(() {
        _isChatInitialized = true;
      });

      // 添加欢迎消息
      ref.read(audioRoomProvider.notifier).addSystemChatMessage('欢迎来到语音房间！');
    } else {
      setState(() {
        _isChatInitialized = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 聊天切换按钮
        _buildChatToggleButton(),

        // 消息显示区域
        Expanded(
          child: _showChatInput ? _buildChatMode() : _buildTraditionalMode(),
        ),

        // 聊天输入框（仅在聊天模式下显示）
        if (_showChatInput) _buildChatInput(),
      ],
    );
  }

  /// 构建聊天切换按钮
  Widget _buildChatToggleButton() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _showChatInput = false;
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: !_showChatInput
                    ? context.theme.colorScheme.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: !_showChatInput
                      ? context.theme.colorScheme.primary
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                '房间动态',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: !_showChatInput
                      ? context.theme.colorScheme.primary
                      : Colors.grey,
                  fontWeight:
                      !_showChatInput ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ),
          8.horizontalSpace,
          GestureDetector(
            onTap: () {
              setState(() {
                _showChatInput = true;
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: _showChatInput
                    ? context.theme.colorScheme.primary.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _showChatInput
                      ? context.theme.colorScheme.primary
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 14.sp,
                    color: _showChatInput
                        ? context.theme.colorScheme.primary
                        : Colors.grey,
                  ),
                  4.horizontalSpace,
                  Text(
                    '文字聊天',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: _showChatInput
                          ? context.theme.colorScheme.primary
                          : Colors.grey,
                      fontWeight:
                          _showChatInput ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const Spacer(),
          // 未读消息计数
          _buildUnreadCount(),
        ],
      ),
    );
  }

  /// 构建未读消息计数
  Widget _buildUnreadCount() {
    final chatMessages = ref.watch(chatMessageProvider);
    final textMessageCount =
        chatMessages.where((m) => m.type == ChatMessageType.text).length;

    if (textMessageCount == 0 || _showChatInput) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        textMessageCount > 99 ? '99+' : textMessageCount.toString(),
        style: TextStyle(
          color: Colors.white,
          fontSize: 10.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建传统模式（房间动态）
  Widget _buildTraditionalMode() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      child: const RoomMessagesList(),
    );
  }

  /// 构建聊天模式
  Widget _buildChatMode() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(
          AppScreenUtils.setRadius(8),
        ),
      ),
      child: const ChatMessageList(
        showSystemMessages: true,
        autoScroll: true,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  /// 构建聊天输入框
  Widget _buildChatInput() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        border: Border(
          top: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: ChatInputField(
        onSendMessage: _handleSendMessage,
        enabled: _isChatInitialized,
        hintText: _isChatInitialized ? '说点什么...' : '聊天功能初始化中...',
        maxLength: 200,
        padding: EdgeInsets.zero,
      ),
    );
  }

  /// 处理发送消息
  Future<void> _handleSendMessage(String message) async {
    if (!_isChatInitialized) return;

    final audioRoomState = ref.read(audioRoomProvider);
    final currentUser = audioRoomState.currentUser;
    final currentUid = audioRoomState.currentUid;
    final roomId = audioRoomState.currentRoom?.id.toString();

    if (currentUser == null || currentUid == null) {
      _showError('用户信息获取失败');
      return;
    }

    // 通过AudioRoomProvider发送聊天消息
    final result = await ref.read(audioRoomProvider.notifier).sendChatMessage(
          content: message,
          roomId: roomId,
        );

    if (result.isLeft()) {
      _showError('发送消息失败');
    } else {
      // 发送成功，消息会通过流自动添加到UI
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('消息发送成功: $message'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 1),
          ),
        );
      }
    }
  }

  /// 显示错误消息
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 切换到聊天模式（供外部调用）
  void showChatMode() {
    setState(() {
      _showChatInput = true;
    });
  }

  /// 切换到传统模式（供外部调用）
  void showTraditionalMode() {
    setState(() {
      _showChatInput = false;
    });
  }

  /// 获取当前是否为聊天模式
  bool get isChatMode => _showChatInput;
}
