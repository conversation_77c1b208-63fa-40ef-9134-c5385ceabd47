import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 聊天输入框组件
class ChatInputField extends ConsumerStatefulWidget {
  final Function(String message) onSendMessage;
  final String? hintText;
  final int maxLength;
  final bool enabled;
  final EdgeInsets? padding;

  const ChatInputField({
    super.key,
    required this.onSendMessage,
    this.hintText,
    this.maxLength = 200,
    this.enabled = true,
    this.padding,
  });

  @override
  ConsumerState<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends ConsumerState<ChatInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isComposing = false;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.3),
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: _buildInputField(),
            ),
            const SizedBox(width: 8.0),
            _buildSendButton(),
          ],
        ),
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(24.0),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        maxLength: widget.maxLength,
        maxLines: 4,
        minLines: 1,
        textInputAction: TextInputAction.send,
        decoration: InputDecoration(
          hintText: widget.hintText ?? '说点什么...',
          hintStyle: TextStyle(
            color: Colors.grey[500],
            fontSize: 14,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 12.0,
          ),
          counterText: '', // 隐藏字符计数
        ),
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
        onChanged: (text) {
          setState(() {
            _isComposing = text.trim().isNotEmpty;
          });
        },
        onSubmitted: (_) => _handleSendMessage(),
      ),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return GestureDetector(
      onTap: _isComposing && widget.enabled ? _handleSendMessage : null,
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: _isComposing && widget.enabled
              ? Theme.of(context).primaryColor
              : Colors.grey[300],
          borderRadius: BorderRadius.circular(22),
        ),
        child: Icon(
          Icons.send,
          color:
              _isComposing && widget.enabled ? Colors.white : Colors.grey[500],
          size: 20,
        ),
      ),
    );
  }

  /// 处理发送消息
  void _handleSendMessage() {
    final message = _controller.text.trim();
    if (message.isEmpty || !widget.enabled) return;

    // 发送消息
    widget.onSendMessage(message);

    // 清空输入框
    _controller.clear();
    setState(() {
      _isComposing = false;
    });

    // 保持焦点
    _focusNode.requestFocus();
  }

  /// 清空输入框
  void clear() {
    _controller.clear();
    setState(() {
      _isComposing = false;
    });
  }

  /// 设置文本
  void setText(String text) {
    _controller.text = text;
    setState(() {
      _isComposing = text.trim().isNotEmpty;
    });
  }

  /// 获取当前文本
  String get text => _controller.text;

  /// 获取焦点
  void requestFocus() {
    _focusNode.requestFocus();
  }

  /// 失去焦点
  void unfocus() {
    _focusNode.unfocus();
  }
}
