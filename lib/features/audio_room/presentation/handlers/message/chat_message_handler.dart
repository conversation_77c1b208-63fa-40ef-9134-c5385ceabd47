import 'dart:convert';
import 'dart:typed_data';

import 'package:agora_rtm/agora_rtm.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_enums.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_message/room_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/domain/services/chat_message_stream.dart';
// import 'package:flutter_audio_room/shared/utils/log_utils.dart';

/// 聊天消息处理器
/// 处理来自Stream Channel的文字聊天消息
class ChatMessageHandler {
  final ChatMessageStream _chatMessageStream;

  ChatMessageHandler(this._chatMessageStream);

  /// 处理Topic消息事件
  void handleTopicMessage(TopicEvent event) {
    if (event.topicName != 'text_chat') return;

    final message = _parseTopicMessage(event.message);
    if (message != null) {
      _chatMessageStream.add(message);
    }
  }

  /// 解析Topic消息
  ChatMessageModel? _parseTopicMessage(Uint8List? messageData) {
    if (messageData == null) return null;

    try {
      final messageString = utf8.decode(messageData);
      final messageJson = json.decode(messageString) as Map<String, dynamic>;
      return ChatMessageModel.fromJson(messageJson);
    } catch (e) {
      print('Failed to parse chat message: $e');
      return null;
    }
  }

  /// 将房间消息转换为聊天消息（用于系统消息整合）
  ChatMessageModel? convertRoomMessageToChatMessage(
      RoomMessageModel roomMessage) {
    // 只处理系统相关的消息
    if (roomMessage.event == null) return null;

    String? content;
    ChatMessageType type = ChatMessageType.system;

    switch (roomMessage.event!) {
      case RoomMessageEvent.userStatus:
        content = _buildUserStatusMessage(roomMessage);
        break;
      case RoomMessageEvent.systemMessage:
        content = roomMessage.content;
        break;
      case RoomMessageEvent.actionMessage:
        content = _buildActionMessage(roomMessage);
        type = ChatMessageType.notification;
        break;
      default:
        return null; // 其他类型的消息不转换
    }

    if (content == null || content.isEmpty) return null;

    return ChatMessageModel(
      id: roomMessage.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: roomMessage.senderId ?? 0,
      senderName: roomMessage.sender?.firstName ?? 'System',
      senderAvatar: roomMessage.sender?.avatarUrl,
      timestamp: roomMessage.createAt ?? DateTime.now().millisecondsSinceEpoch,
      type: type,
      roomId: roomMessage.roomId,
      extra: {
        'originalEvent': roomMessage.event!.name,
        'originalSubtype': roomMessage.eventSubtype?.name,
      },
    );
  }

  /// 构建用户状态消息
  String? _buildUserStatusMessage(RoomMessageModel message) {
    final userName = message.sender?.firstName ?? 'Unknown User';

    switch (message.eventSubtype) {
      case RoomMessageEventSubtype.joinRoom:
        return '$userName 加入了房间';
      case RoomMessageEventSubtype.dropOnMic:
        return '$userName 下麦了';
      case RoomMessageEventSubtype.quitManager:
        return '$userName 退出了管理员';
      default:
        return null;
    }
  }

  /// 构建操作消息
  String? _buildActionMessage(RoomMessageModel message) {
    final userName = message.sender?.firstName ?? 'Unknown User';
    final targetName = message.targetUser?.firstName ?? 'Unknown User';

    switch (message.eventSubtype) {
      case RoomMessageEventSubtype.micRequest:
        return '$userName 申请上麦';
      case RoomMessageEventSubtype.agreeMicRequest:
        return '$userName 同意了 $targetName 的上麦申请';
      case RoomMessageEventSubtype.rejectMicRequest:
        return '$userName 拒绝了 $targetName 的上麦申请';
      case RoomMessageEventSubtype.inviteOnMic:
        return '$userName 邀请 $targetName 上麦';
      case RoomMessageEventSubtype.agreeInviteOnMic:
        return '$targetName 同意了上麦邀请';
      case RoomMessageEventSubtype.rejectInviteOnMic:
        return '$targetName 拒绝了上麦邀请';
      case RoomMessageEventSubtype.inviteManager:
        return '$userName 邀请 $targetName 成为管理员';
      case RoomMessageEventSubtype.agreeInviteManager:
        return '$targetName 成为了管理员';
      case RoomMessageEventSubtype.rejectInviteManager:
        return '$targetName 拒绝了管理员邀请';
      case RoomMessageEventSubtype.removeManager:
        return '$userName 移除了 $targetName 的管理员权限';
      case RoomMessageEventSubtype.muteMic:
        return '$userName 被静音了';
      case RoomMessageEventSubtype.unmuteMic:
        return '$userName 取消静音了';
      case RoomMessageEventSubtype.kickOut:
        return '$targetName 被踢出了房间';
      case RoomMessageEventSubtype.levelup:
        return '$userName 升级了';
      default:
        return null;
    }
  }

  /// 添加系统消息到聊天流
  void addSystemMessage(String content, {String? roomId}) {
    final message = ChatMessageModelExtension.createSystemMessage(
      content: content,
      roomId: roomId,
    );
    _chatMessageStream.add(message);
  }

  /// 处理房间消息并转换为聊天消息
  void handleRoomMessage(RoomMessageModel roomMessage) {
    final chatMessage = convertRoomMessageToChatMessage(roomMessage);
    if (chatMessage != null) {
      _chatMessageStream.add(chatMessage);
    }
  }
}
